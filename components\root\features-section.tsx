"use client";

import { ArrowRightIcon, Plus } from "lucide-react";
import { motion } from "motion/react";

export default function FeaturesSection() {
  return (
    <section className="bg-muted/30 relative px-4 py-24 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        {/* Section Header - Linear Style */}
        <div className="mb-16 grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <h2 className="from-primary via-primary/80 to-primary/70 bg-gradient-to-r bg-clip-text text-5xl leading-tight font-medium text-transparent">
              Made for modern product teams
            </h2>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.1 }}
            className="space-y-4"
          >
            <p className="text-muted-foreground text-lg leading-relaxed font-normal">
              Next Core is shaped by the practices and principles that
              distinguish world-class product teams from the rest: relentless
              focus, fast execution, and a commitment to the quality of craft.
            </p>
            <div className="flex items-center">
              <span className="text-primary flex cursor-pointer items-center gap-2 text-sm font-medium hover:underline">
                Make the switch <ArrowRightIcon className="h-3 w-3" />
              </span>
            </div>
          </motion.div>
        </div>

        {/* Feature Cards Grid - Linear.app Style */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
          className="grid grid-cols-1 gap-2 md:grid-cols-3"
        >
          {/* Card 1 - Purpose-built for product development */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.3 }}
            className="group relative flex h-96 flex-col overflow-hidden rounded-2xl border border-neutral-200/20 bg-neutral-900 p-6 transition-all duration-300 hover:border-neutral-200/40 dark:border-neutral-800/50 dark:bg-neutral-900/80 dark:hover:border-neutral-700/60"
          >
            {/* Visual Element - Project Dashboard Mockup */}
            <div className="flex flex-1 items-center justify-center p-4">
              <div className="relative w-full max-w-xs">
                {/* Main dashboard container */}
                <div className="rounded-lg border border-neutral-700/50 bg-neutral-800/80 p-4 shadow-2xl backdrop-blur-sm">
                  {/* Header */}
                  <div className="mb-3 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-green-500"></div>
                      <div className="text-xs font-medium text-neutral-300">
                        Project Alpha
                      </div>
                    </div>
                    <div className="text-xs text-neutral-500">85%</div>
                  </div>

                  {/* Progress bar */}
                  <div className="mb-4 h-1 rounded-full bg-neutral-700">
                    <div className="h-1 w-4/5 rounded-full bg-gradient-to-r from-blue-500 to-purple-500"></div>
                  </div>

                  {/* Task items */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 rounded bg-neutral-700/50 p-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-blue-400"></div>
                      <div className="text-xs text-neutral-300">
                        API Integration
                      </div>
                    </div>
                    <div className="flex items-center gap-2 rounded bg-neutral-700/50 p-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-green-400"></div>
                      <div className="text-xs text-neutral-300">
                        UI Components
                      </div>
                    </div>
                    <div className="flex items-center gap-2 rounded bg-neutral-700/50 p-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-yellow-400"></div>
                      <div className="text-xs text-neutral-300">
                        Testing Suite
                      </div>
                    </div>
                  </div>
                </div>

                {/* Floating notification */}
                <div className="absolute -top-2 -right-2 rounded-full bg-blue-500 px-2 py-1 text-xs font-medium text-white shadow-lg">
                  New
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="relative mt-auto">
              <h3 className="text-xl font-medium text-white">
                Purpose-built for
                <br />
                product development
              </h3>

              {/* Plus Icon */}
              <div className="border-muted/90 absolute right-0 bottom-0 rounded-full border-2 p-2">
                <Plus className="h-5 w-5 text-neutral-400 transition-colors group-hover:text-white" />
              </div>
            </div>
          </motion.div>

          {/* Card 2 - Designed to move fast */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.4 }}
            className="group relative flex h-96 flex-col overflow-hidden rounded-2xl border border-neutral-200/20 bg-neutral-900 p-6 transition-all duration-300 hover:border-neutral-200/40 dark:border-neutral-800/50 dark:bg-neutral-900/80 dark:hover:border-neutral-700/60"
          >
            {/* Visual Element - Performance Dashboard */}
            <div className="flex flex-1 items-center justify-center p-4">
              <div className="relative w-full max-w-xs">
                {/* Performance metrics container */}
                <div className="rounded-lg border border-neutral-700/50 bg-neutral-800/80 p-4 shadow-2xl backdrop-blur-sm">
                  {/* Header */}
                  <div className="mb-4 flex items-center justify-between">
                    <div className="text-xs font-medium text-neutral-300">
                      Performance
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="h-1.5 w-1.5 rounded-full bg-green-500"></div>
                      <div className="text-xs text-green-400">Live</div>
                    </div>
                  </div>

                  {/* Speed metrics */}
                  <div className="space-y-3">
                    {/* Response time */}
                    <div className="flex items-center justify-between">
                      <div className="text-xs text-neutral-400">
                        Response Time
                      </div>
                      <div className="text-xs font-medium text-green-400">
                        12ms
                      </div>
                    </div>

                    {/* Throughput */}
                    <div className="flex items-center justify-between">
                      <div className="text-xs text-neutral-400">Throughput</div>
                      <div className="text-xs font-medium text-blue-400">
                        2.4k/s
                      </div>
                    </div>

                    {/* CPU usage bar */}
                    <div>
                      <div className="mb-1 flex items-center justify-between">
                        <div className="text-xs text-neutral-400">CPU</div>
                        <div className="text-xs text-neutral-300">23%</div>
                      </div>
                      <div className="h-1 rounded-full bg-neutral-700">
                        <div className="h-1 w-1/4 rounded-full bg-gradient-to-r from-green-500 to-blue-500"></div>
                      </div>
                    </div>

                    {/* Memory usage bar */}
                    <div>
                      <div className="mb-1 flex items-center justify-between">
                        <div className="text-xs text-neutral-400">Memory</div>
                        <div className="text-xs text-neutral-300">45%</div>
                      </div>
                      <div className="h-1 rounded-full bg-neutral-700">
                        <div className="h-1 w-2/5 rounded-full bg-gradient-to-r from-blue-500 to-purple-500"></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Speed indicator */}
                <div className="absolute -top-2 -right-2 flex items-center gap-1 rounded-full bg-green-500 px-2 py-1 text-xs font-medium text-white shadow-lg">
                  <div className="h-1 w-1 rounded-full bg-white"></div>
                  Fast
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="relative mt-auto">
              <h3 className="text-xl font-medium text-white">
                Designed to move fast
              </h3>

              {/* Plus Icon */}
              <div className="border-muted/90 absolute right-0 bottom-0 rounded-full border-2 p-2">
                <Plus className="h-5 w-5 text-neutral-400 transition-colors group-hover:text-white" />
              </div>
            </div>
          </motion.div>

          {/* Card 3 - Crafted to perfection */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.5 }}
            className="group relative flex h-96 flex-col overflow-hidden rounded-2xl border border-neutral-200/20 bg-neutral-900 p-6 transition-all duration-300 hover:border-neutral-200/40 dark:border-neutral-800/50 dark:bg-neutral-900/80 dark:hover:border-neutral-700/60"
          >
            {/* Visual Element - Design System Interface */}
            <div className="flex flex-1 items-center justify-center p-4">
              <div className="relative w-full max-w-xs">
                {/* Design system container */}
                <div className="rounded-lg border border-neutral-700/50 bg-neutral-800/80 p-4 shadow-2xl backdrop-blur-sm">
                  {/* Header */}
                  <div className="mb-4 flex items-center justify-between">
                    <div className="text-xs font-medium text-neutral-300">
                      Design System
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="h-1.5 w-1.5 rounded-full bg-purple-500"></div>
                      <div className="text-xs text-purple-400">v2.1</div>
                    </div>
                  </div>

                  {/* Component grid */}
                  <div className="space-y-3">
                    {/* Buttons row */}
                    <div className="flex items-center gap-2">
                      <div className="rounded bg-blue-500 px-2 py-1 text-xs text-white">
                        Primary
                      </div>
                      <div className="rounded border border-neutral-600 px-2 py-1 text-xs text-neutral-300">
                        Secondary
                      </div>
                    </div>

                    {/* Color palette */}
                    <div className="flex items-center gap-1">
                      <div className="h-4 w-4 rounded bg-blue-500"></div>
                      <div className="h-4 w-4 rounded bg-purple-500"></div>
                      <div className="h-4 w-4 rounded bg-green-500"></div>
                      <div className="h-4 w-4 rounded bg-yellow-500"></div>
                      <div className="h-4 w-4 rounded bg-red-500"></div>
                    </div>

                    {/* Typography */}
                    <div className="space-y-1">
                      <div className="text-xs font-semibold text-neutral-200">
                        Heading
                      </div>
                      <div className="text-xs text-neutral-400">Body text</div>
                      <div className="text-xs text-neutral-500">Caption</div>
                    </div>

                    {/* Icons */}
                    <div className="flex items-center gap-2">
                      <div className="flex h-4 w-4 items-center justify-center rounded bg-neutral-700">
                        <div className="h-2 w-2 rounded-full bg-neutral-400"></div>
                      </div>
                      <div className="flex h-4 w-4 items-center justify-center rounded bg-neutral-700">
                        <div className="h-1 w-2 bg-neutral-400"></div>
                      </div>
                      <div className="flex h-4 w-4 items-center justify-center rounded bg-neutral-700">
                        <div className="h-2 w-1 bg-neutral-400"></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Quality badge */}
                <div className="absolute -top-2 -right-2 flex items-center gap-1 rounded-full bg-purple-500 px-2 py-1 text-xs font-medium text-white shadow-lg">
                  ✨ Perfect
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="relative mt-auto">
              <h3 className="text-xl font-medium text-white">
                Crafted to perfection
              </h3>

              {/* Plus Icon */}
              <div className="border-muted/90 absolute right-0 bottom-0 rounded-full border-2 p-2">
                <Plus className="h-5 w-5 text-neutral-400 transition-colors group-hover:text-white" />
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
