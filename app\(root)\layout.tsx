import GetAuthSession from "@/action/auth/get-auth-session";
import LenisProvider from "@/components/lenis-provider";
import FooterSection from "@/components/root/footer-section";
import TopNav from "@/components/root/top-nav";

export default async function BaseLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const session = await GetAuthSession();

  return (
    <LenisProvider
      root={true}
      options={{ smoothWheel: true }}
    >
      <TopNav session={session} />

      {children}

      <FooterSection />
    </LenisProvider>
  );
}
